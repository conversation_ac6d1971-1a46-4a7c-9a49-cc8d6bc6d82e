<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="instap">
    <!-- 网站图标 -->
    <link rel="icon" href="./favicon.ico">
    <!-- 网站标题 -->
    <title>78研究圈导航页</title>
    <!-- 百度统计 -->
    <script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?1cc27a6767f8329deb4d5b987658f3b8";
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(hm, s);
    })();
    </script>
    <!-- 引用外部CSS文件 -->
    <link href="assets/m.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="assets/style.css">
    <style>
        /* 项目样式 */
        .item {
	transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
}
.item:hover {
	transform: scale(1.05);
	opacity: 0.9;
}

/* 导航按钮悬停效果 */
.tab-item {
	transition: all 0.3s ease-in-out;
	cursor: pointer;
}

.tab-item:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.tab-item:hover .text {
	color: white;
}

.tab-item:active {
	transform: translateY(0px);
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 当前激活按钮样式 */
.tab-item.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	transform: translateY(-1px);
}

.tab-item.active .text {
	color: white;
}

/* 统一应用项目样式 */
.app-item {
	opacity: 1;
}

/* 移动端触摸优化 */
@media (max-width: 768px) {
	.tab-item {
		-webkit-tap-highlight-color: transparent;
		touch-action: manipulation;
	}

	.tab-item:active {
		transform: translateY(-1px) scale(0.98);
		transition: all 0.1s ease-in-out;
	}

	.item {
		-webkit-tap-highlight-color: transparent;
		touch-action: manipulation;
	}
}

/* 网站底部样式 */
.footer {
	text-align: center;
	padding: 10px 0;
	margin-top: 30px;
	font-size: 12px;
	color: #b3b3b3;
}

.footer a {
	color: #b3b3b3;
	text-decoration: none;
	transition: color 0.3s ease;
}

.footer a:hover {
	color: #667eea;
	text-decoration: underline;
}

.footer span {
	margin: 0 5px;
}

/* 移动端footer优化 */
@media (max-width: 768px) {
	.footer {
		font-size: 11px;
		padding: 10px 10px;
		margin: 10px 10px;
	}
}
.modal {
	display:none;
	position:fixed;
	z-index:1;
	padding-top:60px;
	left:0;
	top:0;
	width:100%;
	height:100%;
	overflow:auto;
	background-color:rgb(0,0,0);
	background-color:rgba(0,0,0,0.4)
}
.modal-content {
	margin:5% auto;
	padding:20px;
	border:1px solid #888;
	width:80%;
	max-width:400px
}
.close {
	color:#aaa;
	float:right;
	font-size:28px;
	font-weight:bold
}
.close:hover,.close:focus {
	color:black;
	text-decoration:none;
	cursor:pointer
}
.web_notice {
	position:fixed;
	top:0;
	left:0;
	width:100%;
	height:100%;
	background:rgba(0,0,0,0.3);
	z-index:99999
}
.web_notice_content {
	position:fixed;
	top:50%;
	left:50%;
	width:550px;
	background:#FFF;
	transform:translate(-50%,-50%);
	border-radius:40px;
	padding:50px 40px
}
.web_notice h3 {
	font-weight:bold;
	text-align:center;
	font-size:30px
}
.web_notice p {
	font-size:16px;
	margin-top:26px;
	line-height:30px;
	color:#999
}
.web_notice a {
	display:block;
	background:#98a3ff;
	color:#FFF;
	text-align:center;
	font-weight:bold;
	font-size:19px;
	line-height:60px;
	margin:0 auto;
	margin-top:45px;
	border-radius:32px;
	width:100%
}
@media (max-width:600px) {
	.web_notice_content {
	width:90%;
	padding:30px 20px
}
.web_notice h3 {
	font-size:24px
}
.web_notice a {
	font-size:16px;
	line-height:50px
}
}</style>
</head>
<body>
    <div id="app">
        <div class="header">
            <div class="banner">
                <!-- 网站Logo -->
                <div class="logo">
                    <img src="./assets/tx.png" alt="">
                </div>
            </div>
            <!-- 作者名称 -->
            <div class="author-name">78研究圈</div>
            <!-- 描述 -->
            <p class="desc">建议收藏防止迷 专业效率 / 稳定高效 / 专业品质 / 优质服务 以信誉求发展 以服务求口碑 客服QQ：1690712121 回复慢了请理解 售后不会 超过24小时</p>
        </div>
        <!-- 主要内容 -->
        <div class="body">
            <!-- 导航标签列表 -->
            <div class="tab-list">
                <div class="tab-item zy">
                    <span class="text">导航</span>
                </div>
                <div class="tab-item lx">
                    <span class="text">联系</span>
                </div>
                <div class="tab-item dh">
                    <span class="text">动态</span>
                </div>
            </div>
            <!-- 应用列表1 -->
            <div class="app-list a1">
                <a href="http://q.78190.cn/" class="item"target=”_blank”style="opacity: 1;">
                    <div class="content-wrap">
                        <div class="img-wrap">
                            <img src="./photos/tuijian.png" alt="">
                        </div>
                        <p class="app-name">资源 发布更新公告</p>
                    </div>
                </a>
                <a href="https://www.78190.cn/wp-content/uploads/2025/08/相关须知同意再下.pdf" class="item"target=”_blank”style="opacity: 1;">
                    <div class="content-wrap">
                        <div class="img-wrap">
                            <img src="./photos/shouhou.png" alt="">
                        </div>
                        <p class="app-name">售后必看！点我</p>
                    </div>
                </a>
                <a href="https://qibaquan.feishu.cn/wiki/Mv6pwOBbmiu5u1kfV9NcJvwonch" class="item"target=”_blank”style="opacity: 1;">
                    <div class="content-wrap">
                        <div class="img-wrap">
                            <img src="./photos/feishu.png" alt="">
                        </div>
                        <p class="app-name">关于我们 圈子介绍</p>
                    </div>
                </a>
                <a href="https://engine.78190.cn/" class="item"target=”_blank”style="opacity: 1;">
                    <div class="content-wrap">
                        <div class="img-wrap">
                            <img src="./photos/shop1.png" alt="">
                        </div>
                        <p class="app-name">研究圈1号店</p>
                    </div>
                </a>
                <a href="https://engine.78190.cn/" class="item"target=”_blank”style="opacity: 1;">
                    <div class="content-wrap">
                        <div class="img-wrap">
                            <img src="./photos/shop2.png" alt="">
                        </div>
                        <p class="app-name">研究圈2号店</p>
                    </div>
                </a>
                <a href="https://engine.78190.cn/" class="item"target=”_blank”style="opacity: 1;">
                    <div class="content-wrap">
                        <div class="img-wrap">
                            <img src="./photos/shop2.png" alt="">
                        </div>
                        <p class="app-name">研究圈3号店</p>
                    </div>
                </a>
                <a href="https://h5.lot-ml.com/ProductEn/Index/c50025566d587d65" class="item"target=”_blank”style="opacity: 1;">
                    <div class="content-wrap">
                        <div class="img-wrap">
                            <img src="./photos/liuliangk.png" alt="">
                        </div>
                        <p class="app-name">正规流量卡 免费领</p>
                    </div>
                </a>
                <a href="https://qibaquan.feishu.cn/docx/PE3pdN8yToUSoLxuYvOcsLa5n2c?from=from_copylink" class="item"target=”_blank”style="opacity: 1;">
                    <div class="content-wrap">
                        <div class="img-wrap">
                            <img src="./photos/duijie.png" alt="">
                        </div>
                        <p class="app-name">资源渠道对接</p>
                     </div>
                </a>
                
            </div>
            <!-- 应用列表2 -->
            <div class="app-list a2">
                <a href="javascript:void(0);" class="item" style="opacity: 1;" onclick="showModal('./photos/jywx1.jpg')">                
                    <div class="content-wrap">
                        <div class="img-wrap">
                            <img src="./photos/wxtubiao.png" alt="">
                        </div>
                        <p class="app-name">我的微信</p>
                    </div>
                </a>
                <a href="javascript:void(0);" class="item" style="opacity: 1;" onclick="showModal('./photos/qq714.png')">
                    <div class="content-wrap">
                        <div class="img-wrap">
                            <img src="./photos/qqtubiao.png" alt="">
                        </div>
                        <p class="app-name">我的QQ</p>
                    </div>
                </a>
                <a href="http://lxwm.78190.cn/" class="item"target=”_blank”style="opacity: 1;">
                    <div class="content-wrap">
                        <div class="img-wrap">
                            <img src="./photos/lxwm.png" alt="">
                        </div>
                        <p class="app-name">联系我们</p>
                    </div>
                </a>
                <a href="https://qm.qq.com/q/6H3uHN4ciQ" class="item"target=”_blank”style="opacity: 1;">
                    <div class="content-wrap">
                        <div class="img-wrap">
                            <img src="./photos/qqun.png" alt="">
                        </div>
                        <p class="app-name">资源通知Q群</p>
                    </div>
                </a>
                <a href="https://link3.cc/bx78190" class="item"target=”_blank”style="opacity: 1;">
                    <div class="content-wrap">
                        <div class="img-wrap">
                            <img src="./photos/link3.png" alt="">
                        </div>
                        <p class="app-name">圈子永久导航页2</p>
                    </div>
                </a>
            </div>
            <!-- 应用列表3 -->
            <div class="app-list a3">                
                <a href="https://www.78190.cn/" class="item"target=”_blank”style="opacity: 1;">
                    <div class="content-wrap">
                        <div class="img-wrap">
                            <img src="./photos/shouye.png" alt="">
                        </div>
                        <p class="app-name">圈子官网</p>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- 网站底部信息 -->
    <div class="footer">
        <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener">鲁ICP备2022019221号-1</a>
        <span> | </span>
        <span>©2025 78研究圈 版权所有</span>
    </div>
    <!-- 网站弹窗通知 -->
    <div class="web_notice">
        <div class="web_notice_content">
            <h3>78研究圈通知</h3>
            <p>欢迎来到我的引导页 建议收藏防止迷路</p>
            <p>专业效率 / 稳定高效 / 专业品质 / 优质服务 以信誉求发展 以服务求口碑</p>
            <p>客服QQ：1690712121 回复慢了请理解 售后不会 超过24小时</p>
            <a href="javascript:void(0);" onclick="document.querySelector('.web_notice').remove()">我知道了</a>
        </div>
    </div>
    <!-- 模态框 -->
    <div id="myModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <img id="modalImage" src="" alt="Image" style="width:100%">
        </div>
    </div>
    <!-- 引用jQuery库 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.4/jquery.min.js"></script>

    <script>


        // jQuery文档就绪函数
        jQuery(document).ready(function($) {
            // 代码规范化：统一处理内联样式
            $(".item").addClass("app-item").removeAttr("style");

            // 改进alt属性
            $('img[src*="tuijian"]').attr("alt", "资源发布更新公告");
            $('img[src*="shouhou"]').attr("alt", "售后服务");
            $('img[src*="feishu"]').attr("alt", "关于我们圈子介绍");
            $('img[src*="shop1"]').attr("alt", "研究圈1号店");
            $('img[src*="shop2"]').attr("alt", "研究圈2号店");
            $('img[src*="liuliangk"]').attr("alt", "正规流量卡免费领");
            $('img[src*="duijie"]').attr("alt", "资源渠道对接");
            $('img[src*="wxtubiao"]').attr("alt", "微信联系");
            $('img[src*="qqtubiao"]').attr("alt", "QQ联系");
            $('img[src*="lxwm"]').attr("alt", "联系我们");
            $('img[src*="link3"]').attr("alt", "发布页");

            // 图片加载错误处理
            $("img").on("error", function() {
                $(this).attr("alt", "图片加载失败");
                console.warn("图片加载失败:", $(this).attr("src"));
            });

            // 默认显示第一个应用列表
            $(".a1").css("display", "flex");
            $(".a2").css("display", "none");
            $(".a3").css("display", "none");

            // 默认激活第一个按钮
            $(".zy").addClass("active");

            // 统一的标签切换函数
            function switchTab(activeTab, showList) {
                // 移除所有按钮的激活状态
                $(".tab-item").removeClass("active");
                // 激活当前按钮
                $(activeTab).addClass("active");

                // 隐藏所有列表
                $(".app-list").css("display", "none");
                // 显示指定列表
                $(showList).css("display", "flex");
            }

            // 导航按钮点击事件
            $(".zy").click(function() {
                switchTab(this, ".a1");
            });

            $(".lx").click(function() {
                switchTab(this, ".a2");
            });

            $(".dh").click(function() {
                switchTab(this, ".a3");
            });
 });
        // 显示模态框（改进版）
        function showModal(imageSrc) {
            // 添加加载状态
            $("#modalImage").attr("src", "").attr("alt", "加载中...");
            $("#myModal").css("display", "block");

            // 创建新的图片对象来预加载
            var img = new Image();
            img.onload = function() {
                $("#modalImage").attr("src", imageSrc).attr("alt", "二维码图片");
            };
            img.onerror = function() {
                $("#modalImage").attr("alt", "图片加载失败");
                console.warn("图片加载失败:", imageSrc);
            };
            img.src = imageSrc;
        }

        // 关闭模态框函数
        function closeModal() {
            $("#myModal").css("display", "none");
        }

        // 关闭模态框
        $(".close").click(closeModal);

        // 点击模态框外部关闭模态框
        $(window).click(function(event) {
            if ($(event.target).is("#myModal")) {
                closeModal();
            }
        });

        // 键盘导航支持
        $(document).keydown(function(event) {
            // ESC键关闭模态框
            if (event.keyCode === 27) {
                closeModal();
            }
            // 数字键切换标签页
            if (event.keyCode === 49) { // 按键1
                $(".zy").click();
            } else if (event.keyCode === 50) { // 按键2
                $(".lx").click();
            } else if (event.keyCode === 51) { // 按键3
                $(".dh").click();
            }
        });
    </script>
<script type="text/javascript" id="myhk" src="https://myhkw.cn/api/player/175648917577" key="175648917577" m="1"></script>
<script src="https://player.xfyun.club/js/yinghua.js"></script>
</body>
</html> 